# PierreAuger 带权重多层网络可视化

## 概述
这个程序专门用于可视化 PierreAuger 数据集，该数据集的特点是边权重不相等，需要特殊处理。

## 数据集特点
- **数据集名称**: PierreAuger
- **权重范围**: 0.013158 - 8.416667
- **层数**: 16层
- **节点数**: 大量节点（程序限制显示前300个）
- **边数**: 大量边（过滤后约2603条边）

## 程序特性
1. **权重可视化**: 根据边权重使用不同颜色和线宽
   - 低权重：绿色，细线
   - 中权重：橙色，中等线宽
   - 高权重：红色，粗线

2. **性能优化**: 
   - 限制显示节点数量（最多300个）
   - 使用圆形布局提高计算效率
   - 过滤无效连接

3. **布局特点**:
   - 每层使用圆形背景框
   - 层间连接用点线表示
   - 节点按圆形分布，层次分明

## 输出文件
- **文件位置**: `MLN_SVGs/PierreAuger_weighted.svg`
- **格式**: SVG矢量图
- **分辨率**: 300 DPI

## 使用方法
```bash
cd 文件夹2
python visualize_PierreAuger_weighted.py
```

## 层标签说明
根据数据集，PierreAuger包含以下层：
1. Neutrinos - 中微子层
2. Detector - 探测器层
3. Enhancements - 增强层
4. Anisotropy - 各向异性层
5. Point-source - 点源层
6. Mass-composition - 质量组成层
7. Horizontal - 水平层
8. Hybrid-reconstruction - 混合重建层
9. Spectrum - 光谱层
... 等16层

## 注意事项
- 程序会自动限制节点数量以提高性能
- 中文字体可能显示警告，但不影响图形生成
- 生成的图形包含权重信息的颜色编码

## 技术细节
- **权重颜色映射**: 从绿色到红色的渐变
- **线宽范围**: 0.5 - 3.5 像素
- **布局算法**: 圆形布局 + 层次偏移
- **图形尺寸**: 18x20 英寸
