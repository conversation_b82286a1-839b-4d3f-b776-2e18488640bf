import pathlib
import traceback

from visualize_mln_with_interlayer import draw_multiplex_with_interlayer

ROOT_DIR = pathlib.Path(__file__).parent
MLN_DIR = ROOT_DIR / "MLNDatasets"
OUT_DIR = ROOT_DIR / "MLN_SVGs"

# 可选 tqdm 进度条
try:
	from tqdm import tqdm  # type: ignore
	_TQDM_AVAILABLE = True
except Exception:
	_TQDM_AVAILABLE = False


def is_dataset_dir(p: pathlib.Path) -> bool:
	return p.is_dir() and not p.name.startswith(".")


def main():
	OUT_DIR.mkdir(parents=True, exist_ok=True)
	candidates = sorted([p for p in MLN_DIR.iterdir() if is_dataset_dir(p)])
	total = len(candidates)
	print(f"Found {total} dataset folders under MLNDatasets")

	ok = 0
	errs: list[tuple[str, str]] = []

	if _TQDM_AVAILABLE:
		iterator = tqdm(candidates, desc="Rendering", unit="ds")
	else:
		iterator = candidates

	for ds in iterator:
		ds_name = ds.name
		outfile = OUT_DIR / f"{ds_name}.svg"
		if _TQDM_AVAILABLE:
			iterator.set_description(f"Rendering {ds_name}")
			iterator.refresh()
		else:
			print(f"[+] Rendering {ds_name} -> {outfile}")
		try:
			# 相邻层虚线连接；如需两两层连接可加 all_pairs=False 改为 True
			draw_multiplex_with_interlayer(ds_name, outfile, connect_adjacent_only=True)
			ok += 1
		except Exception as e:
			errs.append((ds_name, f"{type(e).__name__}: {e}"))
			print(f"[!] Failed on {ds_name}: {e}")
			traceback.print_exc()
		finally:
			if _TQDM_AVAILABLE:
				iterator.set_postfix_str(f"ok={ok}, failed={len(errs)}")

	print("\nSummary:")
	print(f"  Succeeded: {ok}")
	print(f"  Failed:   {len(errs)}")
	for name, msg in errs:
		print(f"   - {name}: {msg}")


if __name__ == "__main__":
	main() 