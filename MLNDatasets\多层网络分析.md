### 多层网络（Multilayer Network）分析与实践指南（基于 `MLNDatasets`）

本指南面向本仓库中的多层网络数据集（如 `CKM`、`EUAir`、`CElegans`、`LMT` 等），系统梳理：
- 多层网络的建模方式与常见变体
- 节点数的计量口径（你的问题重点）
- 关键结构特征与如何计算
- 传播/鲁棒性/跨层耦合机制
- 面向工程（含船舶设计）的应用要点
- 实操建议与样例代码

---

## 1. 多层网络是什么
- **多层（multilayer）/复合（multiplex）网络**：同一组节点在多个关系层（layer）上连接，每一层代表一种关系或语义（如 `CKM` 的医生网络：advice、discussion、friend）。
- **层内边（intra-layer edges）**：在同一层内的连接（如“朋友关系层”中的朋友边）。
- **层间边（inter-layer edges）/耦合（coupling）**：跨层的连接，通常用于把同一节点在不同层的“副本（node-layer pair）”耦合起来（本仓库的多数数据集默认不显式给出层间边，可按需要设定耦合方式，如等权一一耦合）。
- **超邻接（supra-adjacency）矩阵**：把所有层内/层间连接拼成一个大矩阵，维度为 `Σk Nk`（若各层节点集合相同且大小为 `N`，且有 `L` 层，则维度为 `L × N`）。

---

## 2. 节点数应如何计（你的问题）
以 `CKM` 为例，层文件给出 `3` 层，节点文件给出一份节点列表，层内边分别存于 `CKM.edges` 中。

- **唯一节点数（unique nodes）N**：去重后的节点个数。若三个层的节点集合相同（多数 multiplex 数据集如此），则唯一节点数 `N` 只计一次。
  - 适用于：按主体数量统计（例如有多少医生/机场/物种）、绘制“聚合图（把多层合成一层）”、跨层对齐的可视化。

- **节点-层副本数（node-layer replicas）L × N**：在超图/超邻接建模中，节点在每一层都有一个“副本”，因此总状态维度随层数线性增长。
  - 适用于：
    - 使用超邻接矩阵进行光谱分析、动态仿真（扩散/流行病/同步）。
    - 跨层传播、层间耦合、阈值推导等理论建模。

- 小结：
  - “每一层的节点是一样的，那算节点数的时候是乘以 3 倍吗还是只算一次呢？”
  - **答案**：
    - 做总体规模、唯一主体数量统计时：**只算一次 `N`**。
    - 做多层动力学/超邻接建模时：**状态维度是 `L × N`**（等价于“乘以层数”），但这不是“唯一节点数”，而是“节点-层副本总数”。

---

## 3. 数据集概览与常见结构特征
本仓库数据集覆盖社会、交通、生物等多域：
- `CKM`（Physicians Innovation）：社会交互多层（advice/discussion/friend）。
- `EUAir`：欧洲机场多层交通网络。
- `CElegans`：秀丽隐杆线虫连接组的分层网络（不同突触/功能层）。
- `LMT`：伦敦交通多层网络，含扰动场景（Disruptions）。
- 其他生物、生态、社交类数据集（`HumanMicrobiome`、`Multi-Soc-wiki-Vote` 等）。

建议统计（每个数据集可按层给出）：
- **唯一节点数** `N`，**层数** `L`。
- **每层边数** `E_l`，平均度 `⟨k_l⟩ = 2E_l/N`（无向）或 `E_l/N`（有向出度均值）。
- **重叠度/多活跃性**：
  - 节点的跨层参与度 `P(v) = |{ l | deg_l(v) > 0 }|`。
  - 节点总度（overlapping degree）`k_over(v) = Σ_l deg_l(v)`。
- **层间相关性**：节点在不同层的度相关、边重叠率（Jaccard of edge sets）。
- **连通性**：各层/聚合图的组件数、最大连通子图规模。
- **社团与耦合模块性**：多层模块性（如 Mucha 等的 multilayer modularity）。
- **可约简性（reducibility）**：层的冗余度，能否合并某些相似层而不损失信息。

---

## 4. 关键指标与计算要点
- **节点度向量** `k(v) = [deg_1(v), …, deg_L(v)]` 与其范数/稀疏度，可刻画节点在各层的重要性分布。
- **重叠度分布** 与 **层参与分布**：衡量多层“多活跃性”。
- **层间度相关系数**（Pearson/Spearman）：判断“在层 A 重要的节点是否也在层 B 重要”。
- **边重叠率** `J(E_i, E_j) = |E_i ∩ E_j| / |E_i ∪ E_j|`：层之间连边的复用程度。
- **多层最短路**：允许在层间耦合边上切换（切层成本可设为 c），可求跨层最短路长度。
- **谱半径/特征值**：基于超邻接矩阵，影响传播阈值与同步性。
- **鲁棒性**：
  - 随机/针对性攻击下的巨型连通分量（GCC）变化（逐步移除节点/边，测量 GCC 大小）。
  - 依赖耦合情况下的级联失效阈值。

---

## 5. 传播、可行性与跨层机制
- **层内传播**：每层可有不同传播率/权重（如航线层 vs 通讯层）。
- **跨层传播（layer switching）**：通过层间耦合把状态从 `v@layer_a` 迁移到 `v@layer_b`，可设切层概率/代价。
- **阈值（以 SIS/SIR 类为例）**：
  - 单层近似阈值与最大特征值 `λ_max(A)` 有关；多层时通常与 `λ_max(Asupra)` 相关（含耦合强度 `ω`）。
  - 增强耦合（`ω ↑`）一般降低传播阈值、加速扩散，但也可能引入跨层依赖导致脆弱性上升。
- **多样路径与冗余**：多层提供更多备选路径，提高可达性与弹性；但若跨层强依赖（interdependency）过强，局部失效可能触发级联。

---

## 6. 面向船舶设计的启示（实例化到工程系统）
船舶/海工系统可自然抽象为多层网络：
- **层划分示例**：
  - 结构层（舱段/骨架连接）、
  - 动力/电力层（能源/配电网络）、
  - 控制/通信层（传感器与总线）、
  - 物流/补给层（港口与航线，或舰内补给路径）。
- **关键问题与对策**：
  - **跨层冗余设计**：在控制/电力层与结构层之间建立低代价耦合与备援路径，提升故障下可达性。
  - **层间解耦与隔离**：对关键舱段或功能模块设置“防火墙”（降低耦合强度或切层概率），抑制故障/火灾/舱室进水的级联传播。
  - **组件重要性评估**：用重叠度、层参与度、跨层介数等识别“多层关键节点/边”，优先加固/冗余。
  - **容错与恢复**：基于多层最短路与替代路径设计应急绕行（例如电力母线切换、通讯备份链路），提高恢复速度。
- **仿真建议**：
  - 构造包含层间耦合的超图模型，设置层内传播率 `β_l` 与层间切换率/代价 `ω`，做场景仿真（故障注入、火灾蔓延、舱室进水、网络攻击），评估阈值与恢复策略。

---

## 7. 实操：如何在本仓库上做基础统计
以下示例基于 `CKM`，复用你已添加的解析与绘图思路，统计每层边数与节点活跃度（示例逻辑，运行前可参考并整合到你的脚本中）。

```python
# 示例：计算唯一节点数 N、层数 L、每层边数、节点跨层参与度
import pathlib
import networkx as nx
from collections import defaultdict

DATASET_DIR = pathlib.Path("MLNDatasets/CKM/Dataset")
EDGES_FILE = DATASET_DIR / "CKM.edges"
NODES_FILE = DATASET_DIR / "CKM-Physicians-Innovation_nodes.txt"

# 读取节点
unique_nodes = set()
with NODES_FILE.open("r", encoding="utf-8") as f:
	for i, line in enumerate(f):
		if i == 0 and "nodeID" in line:
			continue
		parts = line.strip().split()
		if parts:
			unique_nodes.add(int(parts[0]))

# 读取边并按层建图
layer_graphs = defaultdict(lambda: nx.DiGraph())
with EDGES_FILE.open("r", encoding="utf-8") as f:
	for line in f:
		parts = line.strip().split()
		if len(parts) >= 3:
			l, u, v = int(parts[0]), int(parts[1]), int(parts[2])
			w = float(parts[3]) if len(parts) > 3 else 1.0
			layer_graphs[l].add_edge(u, v, weight=w)

# 统计
L = len(layer_graphs)
N = len(unique_nodes)
print("唯一节点数 N:", N)
print("层数 L:", L)
for l, g in sorted(layer_graphs.items()):
	g.add_nodes_from(unique_nodes)
	print(f"层 {l}: 节点={g.number_of_nodes()}, 边={g.number_of_edges()}")

# 跨层参与度 P(v)
participation = {v: 0 for v in unique_nodes}
for v in unique_nodes:
	for g in layer_graphs.values():
		if g.degree(v) > 0:
			participation[v] += 1
print("参与度分布（示例前10个）:", list(participation.items())[:10])
```

---

## 8. 可视化与建模建议
- **可视化**：采用共享布局对齐各层（你当前脚本已实现），方便比较同一节点在不同层的连接差异。
- **建模**：若需开展传播/稳健性分析，补充“层间耦合”并构造超邻接矩阵：
  - 层内块对角是 `A_l`；
  - 层间块可设为 `ω I`（同一节点不同层之间等权耦合），或更复杂的跨层依赖矩阵。
- **仿真**：在超图上进行 SIS/SIR、最短路、级联失效等仿真，观察阈值与敏感性（对 `ω`、分层传播率、切层成本）。

---

## 9. 结论要点（TL;DR）
- **唯一节点数**：多层共享节点的情形下，**只计一次 `N`**。
- **超图维度**：用于动力学/谱分析时，状态维度为 **`L × N`**，不要与“唯一节点数”混淆。
- **跨层耦合**：决定传播阈值与鲁棒性，在工程设计（如船舶）中须在“冗余/连通性”与“隔离/阻断级联”之间折中。
- **实践闭环**：从统计 → 可视化 → 建模 → 仿真 → 设计决策（加固/冗余/隔离/应急路径）。

---

如需，我可以：
- 为每个数据集批量生成统计表（N、L、各层 E、参与度/重叠度分布）。
- 补充“层间耦合”与“超邻接矩阵”构造与仿真脚本，用于传播与级联分析。
- 面向船舶系统定制分层与权重方案，输出冗余与隔离的设计建议清单。 