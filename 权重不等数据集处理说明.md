# 权重不等数据集处理说明

## 问题背景
在多层网络可视化过程中，发现 arXiv-Netscience 和 PierreAuger 这两个数据集存在特殊情况：
- **权重不相等**: 与其他数据集不同，这两个数据集的边权重变化范围很大
- **处理停滞**: 原有的通用可视化程序无法有效处理这些数据集

## 解决方案
为这两个特殊数据集分别创建了专门的处理程序：

### 文件夹1 - arXiv-Netscience 处理
- **程序文件**: `visualize_arXiv_Netscience_weighted.py`
- **输出文件**: `MLN_SVGs/arXiv-Netscience_weighted.svg`
- **特点**: 
  - 权重范围: 0.026 - 17.639
  - 13层网络结构
  - 蓝色到红色的权重颜色映射

### 文件夹2 - PierreAuger 处理
- **程序文件**: `visualize_PierreAuger_weighted.py`
- **输出文件**: `MLN_SVGs/PierreAuger_weighted.svg`
- **特点**:
  - 权重范围: 0.013 - 8.417
  - 16层网络结构
  - 绿色到红色的权重颜色映射

## 技术改进

### 1. 标准可视化风格
- **层内连接**: 实线，每层使用不同颜色（蓝、橙、绿、红等）
- **层间连接**: 虚线，灰色，连接相邻层的相同节点
- **节点样式**: 统一的浅灰色节点，黑色边框
- **布局**: 垂直分层，保持节点相对位置一致

### 2. 权重可视化
- **线宽调整**: 权重越大，边线越粗（0.5-3.0像素）
- **透明度调整**: 权重越大，透明度越高（0.6-0.9）
- **无颜色编码**: 每层使用固定颜色，权重通过线宽和透明度体现

### 3. 性能优化
- **节点限制**:
  - arXiv-Netscience: 最多500个节点
  - PierreAuger: 最多300个节点
- **布局算法**: Spring布局，自适应迭代次数
- **数据过滤**: 只处理选定节点的连接

### 4. 视觉效果
- **层次分明**: 垂直分层，每层Y偏移1.5单位
- **层间连接**: 虚线样式 (0, (4, 4))
- **层标签**: 显示在图形右侧

## 数据集对比

| 数据集 | 权重范围 | 层数 | 显示节点 | 显示边数 | 输出格式 |
|--------|----------|------|----------|----------|----------|
| arXiv-Netscience | 0.026-17.639 | 13 | 500 | 2133 | SVG + PNG |
| PierreAuger | 0.013-8.417 | 16 | 300 | 2603 | SVG + PNG |

## 使用说明

### 运行 arXiv-Netscience 可视化
```bash
cd 文件夹1
python visualize_arXiv_Netscience_weighted.py
```

### 运行 PierreAuger 可视化
```bash
cd 文件夹2
python visualize_PierreAuger_weighted.py
```

## 输出结果
两个程序都会生成高质量的图形文件，包含：
- **SVG矢量图**: 可无限缩放，适合学术发表
- **PNG位图**: 300 DPI，适合文档嵌入
- **标准化风格**: 与现有可视化程序保持一致
- **权重信息**: 通过线宽和透明度体现
- **层标签**: 清晰的层次标识

## 注意事项
1. **字体警告**: 可能出现中文字体缺失警告，但不影响图形生成
2. **处理时间**: 由于数据量大，处理可能需要几分钟时间
3. **内存使用**: 建议在内存充足的环境下运行
4. **文件大小**: 生成的SVG文件可能较大，包含详细的网络信息

## 技术特点
- **标准化风格**: 与现有可视化程序保持一致的视觉风格
- **自适应权重映射**: 根据数据集的实际权重范围调整线宽和透明度
- **智能节点选择**: 优先显示前N个节点，保持网络结构完整性
- **多层布局优化**: Spring布局 + 层次偏移，保持层间关系
- **双格式输出**: SVG矢量图 + PNG位图，满足不同使用需求
- **性能优化**: 自适应算法参数，处理大规模网络数据
