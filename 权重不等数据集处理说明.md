# 权重不等数据集处理说明

## 问题背景
在多层网络可视化过程中，发现 arXiv-Netscience 和 PierreAuger 这两个数据集存在特殊情况：
- **权重不相等**: 与其他数据集不同，这两个数据集的边权重变化范围很大
- **处理停滞**: 原有的通用可视化程序无法有效处理这些数据集

## 解决方案
为这两个特殊数据集分别创建了专门的处理程序：

### 文件夹1 - arXiv-Netscience 处理
- **程序文件**: `visualize_arXiv_Netscience_weighted.py`
- **输出文件**: `MLN_SVGs/arXiv-Netscience_weighted.svg`
- **特点**: 
  - 权重范围: 0.026 - 17.639
  - 13层网络结构
  - 蓝色到红色的权重颜色映射

### 文件夹2 - PierreAuger 处理
- **程序文件**: `visualize_PierreAuger_weighted.py`
- **输出文件**: `MLN_SVGs/PierreAuger_weighted.svg`
- **特点**:
  - 权重范围: 0.013 - 8.417
  - 16层网络结构
  - 绿色到红色的权重颜色映射

## 技术改进

### 1. 权重可视化
- **颜色编码**: 根据权重值分配不同颜色
- **线宽调整**: 权重越大，边线越粗
- **透明度**: 适当的透明度避免重叠遮挡

### 2. 性能优化
- **节点限制**: 
  - arXiv-Netscience: 最多500个节点
  - PierreAuger: 最多300个节点
- **布局算法**: 使用圆形布局替代复杂的弹簧布局
- **数据过滤**: 只处理选定节点的连接

### 3. 视觉效果
- **层次分明**: 每层使用不同的背景标识
- **层间连接**: 用虚线或点线表示层间关系
- **图例完整**: 包含权重范围和层标签说明

## 数据集对比

| 数据集 | 权重范围 | 层数 | 显示节点 | 显示边数 | 颜色方案 |
|--------|----------|------|----------|----------|----------|
| arXiv-Netscience | 0.026-17.639 | 13 | 500 | 2133 | 蓝→红 |
| PierreAuger | 0.013-8.417 | 16 | 300 | 2603 | 绿→红 |

## 使用说明

### 运行 arXiv-Netscience 可视化
```bash
cd 文件夹1
python visualize_arXiv_Netscience_weighted.py
```

### 运行 PierreAuger 可视化
```bash
cd 文件夹2
python visualize_PierreAuger_weighted.py
```

## 输出结果
两个程序都会生成高质量的SVG矢量图，包含：
- 完整的多层网络结构
- 基于权重的颜色编码
- 详细的图例说明
- 层标签和统计信息

## 注意事项
1. **字体警告**: 可能出现中文字体缺失警告，但不影响图形生成
2. **处理时间**: 由于数据量大，处理可能需要几分钟时间
3. **内存使用**: 建议在内存充足的环境下运行
4. **文件大小**: 生成的SVG文件可能较大，包含详细的网络信息

## 技术特点
- **自适应权重映射**: 根据数据集的实际权重范围调整颜色映射
- **智能节点选择**: 优先显示连接度高的重要节点
- **多层布局优化**: 保持层间关系的同时优化视觉效果
- **高质量输出**: 300 DPI的矢量图适合学术发表
