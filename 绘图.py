import matplotlib.pyplot as plt
import networkx as nx

# === 1. 多层网络 (General Multilayer) ===
def draw_multilayer():
    # Layer1
    G1 = nx.Graph()
    G1.add_edges_from([("A1", "B1"), ("B1", "C1")])
    pos1 = nx.spring_layout(G1, seed=42)
    pos1 = {k:(v[0], v[1]+1) for k,v in pos1.items()}  # 上移

    # Layer2
    G2 = nx.Graph()
    G2.add_edges_from([("A2", "B2"), ("B2", "D2")])
    pos2 = nx.spring_layout(G2, seed=42)

    # 跨层连接
    inter_edges = [("A1","A2"), ("B1","B2")]

    plt.subplot(2,2,1)
    nx.draw(G1, pos1, with_labels=True, node_color='skyblue', edge_color='gray')
    nx.draw(G2, pos2, with_labels=True, node_color='lightgreen', edge_color='gray')
    nx.draw_networkx_edges(nx.Graph(inter_edges), pos={**pos1, **pos2}, edge_color='red', style='dashed')
    plt.title("Multilayer Network")

# === 2. 多重网络 (Multiplex Network) ===
def draw_multiplex():
    # 节点相同，关系不同
    nodes = ["A", "B", "C"]
    # Layer1: 好友关系
    layer1_edges = [("A", "B"), ("B", "C")]
    # Layer2: 同事关系
    layer2_edges = [("A", "C")]

    pos = {"A":(0,0), "B":(1,0), "C":(0.5,0.8)}
    plt.subplot(2,2,2)
    nx.draw_networkx_nodes(nx.Graph(), pos, nodelist=nodes, node_color='skyblue')
    nx.draw_networkx_labels(nx.Graph(), pos, labels={n:n for n in nodes})
    nx.draw_networkx_edges(nx.Graph(layer1_edges), pos, edge_color='blue', width=2, label="Friend")
    nx.draw_networkx_edges(nx.Graph(layer2_edges), pos, edge_color='green', width=2, style='dashed', label="Colleague")
    plt.legend()
    plt.title("Multiplex Network")

# === 3. 网络的网络 (Networks of Networks) ===
def draw_networks_of_networks():
    # 网络1 (交通)
    T = nx.Graph()
    T.add_edges_from([("T1","T2"), ("T2","T3")])
    posT = {"T1":(0,0), "T2":(1,0.5), "T3":(2,0)}

    # 网络2 (电力)
    P = nx.Graph()
    P.add_edges_from([("P1","P2"), ("P2","P3")])
    posP = {"P1":(0,-1.5), "P2":(1,-1.0), "P3":(2,-1.5)}

    # 跨网络连接
    inter_edges = [("T2","P2")]

    plt.subplot(2,2,3)
    nx.draw(T, posT, with_labels=True, node_color='orange', edge_color='gray', label="Transport")
    nx.draw(P, posP, with_labels=True, node_color='lightblue', edge_color='gray', label="Power")
    nx.draw_networkx_edges(nx.Graph(inter_edges), pos={**posT, **posP}, edge_color='red', width=2)
    plt.title("Networks of Networks")

# === 4. 异质多层网络 (Heterogeneous Multilayer) ===
def draw_heterogeneous():
    # 异质节点：医生(Dr)、病人(Pt)、医院(Hosp)
    nodes = ["Dr1", "Dr2", "Pt1", "Pt2", "Hosp1"]
    colors = {"Dr1":"pink", "Dr2":"pink", "Pt1":"lightgreen", "Pt2":"lightgreen", "Hosp1":"lightblue"}
    edges1 = [("Dr1","Pt1"), ("Dr2","Pt2")]  # 医患关系
    edges2 = [("Pt1","Hosp1"), ("Pt2","Hosp1")]  # 就诊关系

    pos = {"Dr1":(0,0), "Pt1":(1,0), "Hosp1":(2,0), "Pt2":(1,-1), "Dr2":(0,-1)}
    plt.subplot(2,2,4)
    nx.draw_networkx_nodes(nx.Graph(), pos, nodelist=nodes, node_color=[colors[n] for n in nodes])
    nx.draw_networkx_labels(nx.Graph(), pos)
    nx.draw_networkx_edges(nx.Graph(edges1), pos, edge_color='blue', width=2, label="Doctor-Patient")
    nx.draw_networkx_edges(nx.Graph(edges2), pos, edge_color='purple', width=2, style='dashed', label="Visit")
    plt.legend()
    plt.title("Heterogeneous Multilayer")

# 绘制所有图
plt.figure(figsize=(12, 10))
draw_multilayer()
draw_multiplex()
draw_networks_of_networks()
draw_heterogeneous()
plt.tight_layout()
plt.show()
