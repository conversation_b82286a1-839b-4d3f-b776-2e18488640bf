# Multi_lastfm_asia 分层文件多层网络可视化

## 概述
这个程序专门用于可视化 Multi_lastfm_asia 数据集，该数据集的特点是数据分布在不同的层文件中，包含混合类型的节点ID（数字和字符串）。

## 数据集特点
- **数据集名称**: Multi_lastfm_asia
- **数据结构**: 分层文件格式
  - `layer0.txt`: 第0层网络数据
  - `layer1.txt`: 第1层网络数据
- **层数**: 2层
- **节点数**: 7624个节点（显示前600个）
- **边数**: 总计42890条边（过滤后256条）

## 数据格式
每个层文件的格式为：
```
节点1 节点2
节点1 节点3
...
```
例如：
```
0 747
747 4704
ceng'j0 4164
```

## 程序特性
1. **标准可视化风格**: 
   - **层内连接**: 实线，每层使用不同颜色（蓝、橙）
   - **层间连接**: 虚线，灰色，连接相邻层的相同节点
   - **节点样式**: 统一的浅灰色节点，黑色边框

2. **混合节点ID处理**: 
   - 自动识别数字和字符串节点ID
   - 节点ID标准化：将字符串节点映射为整数
   - 保持节点关系的完整性

3. **分层文件处理**: 
   - 自动识别和加载所有layer*.txt文件
   - 按层ID排序处理
   - 支持混合类型节点ID

4. **性能优化**: 
   - 限制显示节点数量（最多600个）
   - 使用Spring布局算法，自适应迭代次数
   - 过滤无效连接

5. **布局特点**:
   - 垂直分层，每层有固定的Y偏移
   - 保持节点在不同层的相对位置一致
   - 层标签显示在右侧

## 输出文件
- **SVG文件**: `MLN_SVGs/Multi_lastfm_asia.svg` - 矢量图，可无限缩放
- **PNG文件**: `MLN_SVGs/Multi_lastfm_asia.png` - 位图，300 DPI
- **图形尺寸**: 自适应，基于层数调整高度

## 使用方法
```bash
cd 文件夹4
python visualize_Multi_lastfm_asia.py
```

## 层信息说明
- **Layer 0**: 7624个节点，27806条边
- **Layer 1**: 7624个节点，15084条边

## 节点ID标准化
程序会自动处理混合类型的节点ID：
- **数字节点**: 保持原有ID
- **字符串节点**: 映射为连续的整数ID
- **映射过程**: 按字典序排序后分配新ID
- **关系保持**: 确保边的连接关系不变

## 注意事项
- 程序会自动限制节点数量以提高性能
- 中文字体可能显示警告，但不影响图形生成
- 节点ID标准化过程会在控制台显示进度
- 由于节点数量庞大，过滤后的边数可能较少

## 技术细节
- **布局算法**: Spring布局 + 层次偏移
- **层间距**: 1.5 单位
- **节点大小**: 35 像素
- **虚线样式**: (0, (4, 4)) - 4像素线段，4像素间隔
- **文件编码**: UTF-8-sig，支持BOM
- **错误处理**: 自动跳过无效行和空行
- **节点映射**: 字符串到整数的双向映射

## 数据来源
Multi_lastfm_asia 数据集来源于Last.fm音乐平台的亚洲用户数据，包含用户之间的多层关系网络。

## 性能说明
由于原始数据集规模较大（7624个节点，42890条边），程序采用了以下优化策略：
- 限制显示节点数量为600个
- 使用高效的Spring布局算法
- 自适应调整算法参数
- 过滤后的网络更适合可视化分析
