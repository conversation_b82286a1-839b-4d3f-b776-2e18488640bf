import pathlib
import matplotlib
matplotlib.use("Agg")
import matplotlib.pyplot as plt
import networkx as nx
import numpy as np
from matplotlib.patches import Rectangle
import matplotlib.patches as mpatches

ROOT_DIR = pathlib.Path(__file__).parent.parent
MLN_DIR = ROOT_DIR / "MLNDatasets"


def load_layers(layer_file_path: pathlib.Path) -> dict:
    """加载层信息"""
    mapping = {}
    if not layer_file_path.exists():
        return mapping
    
    with layer_file_path.open("r", encoding="utf-8-sig", errors="replace") as f:
        for idx, line in enumerate(f):
            line = line.strip()
            if not line:
                continue
            if idx == 0 and ("layerid" in line.lower() or "layer" in line.lower()):
                # 跳过表头
                continue
            parts = line.split()
            if len(parts) >= 2 and parts[0].isdigit():
                mapping[int(parts[0])] = parts[1]
    return mapping


def load_weighted_layer_graphs(edges_file_path: pathlib.Path, directed: bool = False) -> dict:
    """加载带权重的多层网络图"""
    graphs: dict[int, nx.Graph] = {}
    edge_weights = {}  # 存储边权重信息
    
    with edges_file_path.open("r", encoding="utf-8-sig", errors="replace") as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            parts = line.split()
            # 格式：layer u v weight
            if len(parts) < 4:
                continue
            try:
                layer_id = int(parts[0])
                u = int(parts[1])
                v = int(parts[2])
                w = float(parts[3])
            except Exception:
                continue
                
            if layer_id not in graphs:
                graphs[layer_id] = nx.DiGraph() if directed else nx.Graph()
                edge_weights[layer_id] = {}
                
            graphs[layer_id].add_edge(u, v, weight=w)
            edge_weights[layer_id][(u, v)] = w
            
    return graphs, edge_weights


def get_weight_color(weight, min_weight, max_weight):
    """根据权重获取颜色"""
    if max_weight == min_weight:
        return 'blue'
    
    # 归一化权重到0-1范围
    normalized = (weight - min_weight) / (max_weight - min_weight)
    
    # 使用颜色映射：从浅蓝色到深红色
    if normalized < 0.5:
        # 浅蓝色到蓝色
        intensity = normalized * 2
        return (0.5 + intensity * 0.5, 0.5 + intensity * 0.5, 1.0)
    else:
        # 蓝色到红色
        intensity = (normalized - 0.5) * 2
        return (1.0, 0.5 - intensity * 0.5, 1.0 - intensity)


def compute_layered_positions(graphs: dict[int, nx.Graph], all_nodes: set,
                            y_gap: float = 2.0, seed: int = 42) -> dict:
    """计算多层网络的节点位置（优化版本）"""
    np.random.seed(seed)

    # 限制节点数量以提高性能
    max_nodes = 500
    if len(all_nodes) > max_nodes:
        # 随机选择一部分节点进行可视化
        all_nodes = set(list(all_nodes)[:max_nodes])
        print(f"节点数量过多，仅显示前{max_nodes}个节点")

    # 为所有节点生成基础位置
    base_pos = {}
    if all_nodes:
        # 使用圆形布局，更快且效果好
        n_nodes = len(all_nodes)
        angles = np.linspace(0, 2*np.pi, n_nodes, endpoint=False)
        radius = 1.0

        for i, node in enumerate(sorted(all_nodes)):
            angle = angles[i]
            base_pos[node] = (radius * np.cos(angle), radius * np.sin(angle))

    # 为每层分配位置
    layered_pos = {}
    layer_ids = sorted(graphs.keys())

    for i, layer_id in enumerate(layer_ids):
        layer_pos = {}
        y_offset = i * y_gap

        # 只处理在选定节点集合中的节点
        layer_nodes = set(graphs[layer_id].nodes()) & all_nodes

        for node in layer_nodes:
            if node in base_pos:
                x, y = base_pos[node]
                layer_pos[node] = (x, y + y_offset)
            else:
                # 如果节点不在基础位置中，随机分配
                angle = np.random.random() * 2 * np.pi
                layer_pos[node] = (np.cos(angle), np.sin(angle) + y_offset)

        layered_pos[layer_id] = layer_pos

    return layered_pos


def draw_weighted_multiplex(dataset_name: str, output_path: pathlib.Path, 
                          connect_adjacent_only: bool = True, figsize=(20, 15)):
    """绘制带权重的多层网络"""
    
    # 加载数据
    dataset_dir = MLN_DIR / dataset_name
    data_dir = dataset_dir / "Dataset"
    
    edges_file = data_dir / f"{dataset_name}.edges"
    layers_file = data_dir / f"{dataset_name.lower().replace('-', '_')}_layers.txt"
    
    if not edges_file.exists():
        raise FileNotFoundError(f"Edges file not found: {edges_file}")
    
    # 加载层信息和图数据
    layer_mapping = load_layers(layers_file)
    graphs, edge_weights = load_weighted_layer_graphs(edges_file, directed=False)
    
    if not graphs:
        raise ValueError("No graphs loaded")
    
    # 获取所有节点（限制数量）
    all_nodes = set()
    for graph in graphs.values():
        all_nodes.update(graph.nodes())

    # 限制节点数量以提高性能
    max_nodes = 500
    if len(all_nodes) > max_nodes:
        all_nodes = set(list(all_nodes)[:max_nodes])
        print(f"节点数量过多，仅显示前{max_nodes}个节点")

    # 过滤图，只保留选定的节点
    filtered_graphs = {}
    filtered_edge_weights = {}
    for layer_id, graph in graphs.items():
        filtered_graph = graph.subgraph(all_nodes).copy()
        if filtered_graph.number_of_nodes() > 0:
            filtered_graphs[layer_id] = filtered_graph
            filtered_edge_weights[layer_id] = {
                edge: weight for edge, weight in edge_weights[layer_id].items()
                if edge[0] in all_nodes and edge[1] in all_nodes
            }

    # 计算权重范围
    all_weights = []
    for layer_weights in filtered_edge_weights.values():
        all_weights.extend(layer_weights.values())

    min_weight = min(all_weights) if all_weights else 0
    max_weight = max(all_weights) if all_weights else 1

    print(f"权重范围: {min_weight:.6f} - {max_weight:.6f}")
    print(f"过滤后的层数: {len(filtered_graphs)}")
    print(f"过滤后的节点数: {len(all_nodes)}")

    # 计算位置
    layered_pos = compute_layered_positions(filtered_graphs, all_nodes)
    
    # 创建图形
    fig, ax = plt.subplots(figsize=figsize)

    layer_ids = sorted(filtered_graphs.keys())
    colors = plt.cm.Set3(np.linspace(0, 1, len(layer_ids)))

    # 绘制每一层
    for i, layer_id in enumerate(layer_ids):
        graph = filtered_graphs[layer_id]
        pos = layered_pos[layer_id]
        layer_color = colors[i]
        
        # 绘制层背景
        if pos:
            x_coords = [pos[node][0] for node in pos]
            y_coords = [pos[node][1] for node in pos]
            
            x_min, x_max = min(x_coords) - 0.1, max(x_coords) + 0.1
            y_min, y_max = min(y_coords) - 0.1, max(y_coords) + 0.1
            
            rect = Rectangle((x_min, y_min), x_max - x_min, y_max - y_min,
                           linewidth=2, edgecolor=layer_color, facecolor='none', alpha=0.3)
            ax.add_patch(rect)
        
        # 绘制边（根据权重着色）
        for edge in graph.edges():
            u, v = edge
            if u in pos and v in pos:
                x1, y1 = pos[u]
                x2, y2 = pos[v]

                weight = filtered_edge_weights[layer_id].get((u, v), 1.0)
                edge_color = get_weight_color(weight, min_weight, max_weight)

                # 根据权重调整线宽
                line_width = 0.5 + (weight - min_weight) / (max_weight - min_weight) * 2

                ax.plot([x1, x2], [y1, y2], color=edge_color, linewidth=line_width, alpha=0.7)
        
        # 绘制节点
        node_x = [pos[node][0] for node in graph.nodes() if node in pos]
        node_y = [pos[node][1] for node in graph.nodes() if node in pos]
        
        ax.scatter(node_x, node_y, c=[layer_color], s=50, alpha=0.8, edgecolors='black', linewidth=0.5)
        
        # 添加层标签
        layer_label = layer_mapping.get(layer_id, f"Layer {layer_id}")
        if pos:
            y_center = np.mean([pos[node][1] for node in pos])
            ax.text(-0.15, y_center, layer_label, fontsize=10, ha='right', va='center',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor=layer_color, alpha=0.7))
    
    # 绘制层间连接（如果需要）
    if connect_adjacent_only and len(layer_ids) > 1:
        for i in range(len(layer_ids) - 1):
            layer1_id = layer_ids[i]
            layer2_id = layer_ids[i + 1]
            
            pos1 = layered_pos[layer1_id]
            pos2 = layered_pos[layer2_id]
            
            # 连接相同的节点
            common_nodes = set(pos1.keys()) & set(pos2.keys())
            for node in common_nodes:
                x1, y1 = pos1[node]
                x2, y2 = pos2[node]
                ax.plot([x1, x2], [y1, y2], 'gray', linewidth=0.5, alpha=0.3, linestyle='--')
    
    # 设置图形属性
    ax.set_title(f"多层网络可视化: {dataset_name}\n(权重范围: {min_weight:.3f} - {max_weight:.3f})", 
                fontsize=16, pad=20)
    ax.set_aspect('equal')
    ax.axis('off')
    
    # 添加图例
    legend_elements = []
    for i, layer_id in enumerate(layer_ids):
        layer_label = layer_mapping.get(layer_id, f"Layer {layer_id}")
        legend_elements.append(mpatches.Patch(color=colors[i], label=layer_label))
    
    # 添加权重颜色图例
    legend_elements.append(mpatches.Patch(color='lightblue', label=f'低权重 ({min_weight:.3f})'))
    legend_elements.append(mpatches.Patch(color='red', label=f'高权重 ({max_weight:.3f})'))
    
    ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.15, 1))
    
    plt.tight_layout()
    
    # 确保输出目录存在
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 保存图形
    plt.savefig(output_path, dpi=300, bbox_inches='tight', format='svg')
    plt.close()
    
    print(f"图形已保存到: {output_path}")
    print(f"数据集: {dataset_name}")
    print(f"原始层数: {len(graphs)}, 过滤后层数: {len(filtered_graphs)}")
    print(f"显示节点数: {len(all_nodes)}")
    print(f"显示边总数: {sum(g.number_of_edges() for g in filtered_graphs.values())}")


if __name__ == "__main__":
    output_dir = pathlib.Path("MLN_SVGs")
    output_dir.mkdir(exist_ok=True)
    
    output_path = output_dir / "arXiv-Netscience_weighted.svg"
    draw_weighted_multiplex("arXiv-Netscience", output_path, connect_adjacent_only=True)
    print("完成 arXiv-Netscience 带权重可视化")
