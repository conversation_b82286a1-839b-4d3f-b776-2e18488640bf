import pathlib
import matplotlib
matplotlib.use("Agg")
import matplotlib.pyplot as plt
import networkx as nx
import numpy as np

ROOT_DIR = pathlib.Path(__file__).parent.parent
MLN_DIR = ROOT_DIR / "MLNDatasets"


def load_layers(layer_file_path: pathlib.Path) -> dict:
    """加载层信息"""
    mapping = {}
    if not layer_file_path.exists():
        return mapping
    
    with layer_file_path.open("r", encoding="utf-8-sig", errors="replace") as f:
        for idx, line in enumerate(f):
            line = line.strip()
            if not line:
                continue
            if idx == 0 and ("layerid" in line.lower() or "layer" in line.lower()):
                # 跳过表头
                continue
            parts = line.split()
            if len(parts) >= 2 and parts[0].isdigit():
                mapping[int(parts[0])] = parts[1]
    return mapping


def load_weighted_layer_graphs(edges_file_path: pathlib.Path, directed: bool = False) -> dict:
    """加载带权重的多层网络图"""
    graphs: dict[int, nx.Graph] = {}
    edge_weights = {}  # 存储边权重信息
    
    with edges_file_path.open("r", encoding="utf-8-sig", errors="replace") as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            parts = line.split()
            # 格式：layer u v weight
            if len(parts) < 4:
                continue
            try:
                layer_id = int(parts[0])
                u = int(parts[1])
                v = int(parts[2])
                w = float(parts[3])
            except Exception:
                continue
                
            if layer_id not in graphs:
                graphs[layer_id] = nx.DiGraph() if directed else nx.Graph()
                edge_weights[layer_id] = {}
                
            graphs[layer_id].add_edge(u, v, weight=w)
            edge_weights[layer_id][(u, v)] = w
            
    return graphs, edge_weights


def get_weight_color(weight, min_weight, max_weight):
    """根据权重获取颜色"""
    if max_weight == min_weight:
        return 'blue'
    
    # 归一化权重到0-1范围
    normalized = (weight - min_weight) / (max_weight - min_weight)
    
    # 使用颜色映射：从浅蓝色到深红色
    if normalized < 0.5:
        # 浅蓝色到蓝色
        intensity = normalized * 2
        return (0.5 + intensity * 0.5, 0.5 + intensity * 0.5, 1.0)
    else:
        # 蓝色到红色
        intensity = (normalized - 0.5) * 2
        return (1.0, 0.5 - intensity * 0.5, 1.0 - intensity)


def compute_layered_positions(graphs: dict[int, nx.Graph], all_nodes: set,
                            y_gap: float = 1.5, seed: int = 42, iterations: int = 50) -> dict:
    """计算多层网络的节点位置（参考标准风格）"""
    np.random.seed(seed)

    # 限制节点数量以提高性能
    max_nodes = 500
    if len(all_nodes) > max_nodes:
        all_nodes = set(list(all_nodes)[:max_nodes])
        print(f"节点数量过多，仅显示前{max_nodes}个节点")

    # 自适应选择布局策略
    n = len(all_nodes)
    if n > 2000:
        iterations = 20
    elif n > 1000:
        iterations = 30
    else:
        iterations = 50

    # 创建包含所有节点的临时图
    temp_graph = nx.Graph()
    temp_graph.add_nodes_from(all_nodes)

    # 添加所有层的边来改善布局
    for layer_graph in graphs.values():
        for u, v in layer_graph.edges():
            if u in all_nodes and v in all_nodes:
                temp_graph.add_edge(u, v)

    # 生成基础布局
    if temp_graph.number_of_edges() > 0:
        base_pos = nx.spring_layout(temp_graph, k=1, iterations=iterations, seed=seed)
    else:
        # 如果没有边，使用圆形布局
        base_pos = nx.circular_layout(temp_graph)

    # 为每层分配位置
    positions = {}
    layer_ids = sorted(graphs.keys())

    for i, layer_id in enumerate(layer_ids):
        offset_y = i * y_gap
        for v in all_nodes:
            x, y = base_pos.get(v, (0.0, 0.0))
            positions[(layer_id, v)] = (x, y + offset_y)

    return positions


def draw_weighted_multiplex(dataset_name: str, output_path: pathlib.Path,
                          connect_adjacent_only: bool = True):
    """绘制带权重的多层网络"""
    
    # 加载数据
    dataset_dir = MLN_DIR / dataset_name
    data_dir = dataset_dir / "Dataset"
    
    edges_file = data_dir / f"{dataset_name}.edges"
    layers_file = data_dir / f"{dataset_name.lower().replace('-', '_')}_layers.txt"
    
    if not edges_file.exists():
        raise FileNotFoundError(f"Edges file not found: {edges_file}")
    
    # 加载层信息和图数据
    layer_mapping = load_layers(layers_file)
    graphs, edge_weights = load_weighted_layer_graphs(edges_file, directed=False)
    
    if not graphs:
        raise ValueError("No graphs loaded")
    
    # 获取所有节点（限制数量）
    all_nodes = set()
    for graph in graphs.values():
        all_nodes.update(graph.nodes())

    # 限制节点数量以提高性能
    max_nodes = 500
    if len(all_nodes) > max_nodes:
        all_nodes = set(list(all_nodes)[:max_nodes])
        print(f"节点数量过多，仅显示前{max_nodes}个节点")

    # 过滤图，只保留选定的节点
    filtered_graphs = {}
    filtered_edge_weights = {}
    for layer_id, graph in graphs.items():
        filtered_graph = graph.subgraph(all_nodes).copy()
        if filtered_graph.number_of_nodes() > 0:
            filtered_graphs[layer_id] = filtered_graph
            filtered_edge_weights[layer_id] = {
                edge: weight for edge, weight in edge_weights[layer_id].items()
                if edge[0] in all_nodes and edge[1] in all_nodes
            }

    # 计算权重范围
    all_weights = []
    for layer_weights in filtered_edge_weights.values():
        all_weights.extend(layer_weights.values())

    min_weight = min(all_weights) if all_weights else 0
    max_weight = max(all_weights) if all_weights else 1

    print(f"权重范围: {min_weight:.6f} - {max_weight:.6f}")
    print(f"过滤后的层数: {len(filtered_graphs)}")
    print(f"过滤后的节点数: {len(all_nodes)}")

    # 计算位置（使用标准风格）
    positions = compute_layered_positions(filtered_graphs, all_nodes)

    # 创建图形（使用标准风格）
    layer_ids = sorted(filtered_graphs.keys())
    colors = [
        "tab:blue", "tab:orange", "tab:green", "tab:red", "tab:purple",
        "tab:brown", "tab:pink", "tab:gray", "tab:olive", "tab:cyan",
        "tab:blue", "tab:orange", "tab:green"  # 扩展颜色以支持更多层
    ]

    plt.figure(figsize=(10, 2 + 2 * len(layer_ids)))
    ax = plt.gca()

    # 绘制层内连接（实线，不同颜色）
    for idx, layer_id in enumerate(layer_ids):
        graph = filtered_graphs[layer_id]
        layer_color = colors[idx % len(colors)]

        # 为当前层的图添加所有节点（保持布局一致性）
        graph_with_all_nodes = graph.copy()
        graph_with_all_nodes.add_nodes_from(all_nodes)

        # 绘制节点
        nx.draw_networkx_nodes(
            graph_with_all_nodes,
            {v: positions[(layer_id, v)] for v in all_nodes},
            node_size=35,
            node_color="#f0f0f0",
            edgecolors="#333333",
            linewidths=0.4,
            ax=ax,
        )

        # 绘制边（实线，根据权重调整透明度和线宽）
        for u, v in graph.edges():
            if u in all_nodes and v in all_nodes:
                pos_u = positions[(layer_id, u)]
                pos_v = positions[(layer_id, v)]

                weight = filtered_edge_weights[layer_id].get((u, v), 1.0)
                # 根据权重调整线宽和透明度
                line_width = 0.5 + (weight - min_weight) / (max_weight - min_weight) * 2.5
                alpha = 0.6 + (weight - min_weight) / (max_weight - min_weight) * 0.3

                ax.plot([pos_u[0], pos_v[0]], [pos_u[1], pos_v[1]],
                       color=layer_color, linewidth=line_width, alpha=alpha)

        # 添加层标签
        ys = [positions[(layer_id, v)][1] for v in all_nodes]
        y_level = sum(ys) / len(ys) if ys else idx * 1.5
        layer_label = layer_mapping.get(layer_id, f"Layer {layer_id}")
        ax.text(1.02, y_level, f"Layer {layer_id}: {layer_label}",
               transform=ax.transData, fontsize=9, va='center')
    
    # 绘制层间连接（虚线，标准风格）
    if connect_adjacent_only and len(layer_ids) > 1:
        for i in range(len(layer_ids) - 1):
            layer1_id = layer_ids[i]
            layer2_id = layer_ids[i + 1]

            # 连接相邻层的相同节点
            for v in all_nodes:
                pos1 = positions[(layer1_id, v)]
                pos2 = positions[(layer2_id, v)]
                ax.plot([pos1[0], pos2[0]], [pos1[1], pos2[1]],
                       linestyle=(0, (4, 4)), color="#777777", linewidth=0.6, alpha=0.7)
    
    # 设置图形属性（标准风格）
    ax.axis("off")
    plt.tight_layout()

    # 确保输出目录存在
    output_path.parent.mkdir(parents=True, exist_ok=True)

    # 保存图形（保存SVG和PNG两种格式）
    stem = output_path.with_suffix("")
    svg_path = stem.with_suffix(".svg")
    png_path = stem.with_suffix(".png")

    plt.savefig(svg_path, dpi=300, bbox_inches="tight")
    plt.savefig(png_path, dpi=300, bbox_inches="tight")
    plt.close()

    print(f"Saved figures to: {svg_path} and {png_path}")
    
    print(f"图形已保存到: {output_path}")
    print(f"数据集: {dataset_name}")
    print(f"原始层数: {len(graphs)}, 过滤后层数: {len(filtered_graphs)}")
    print(f"显示节点数: {len(all_nodes)}")
    print(f"显示边总数: {sum(g.number_of_edges() for g in filtered_graphs.values())}")


if __name__ == "__main__":
    output_dir = pathlib.Path("MLN_SVGs")
    output_dir.mkdir(exist_ok=True)
    
    output_path = output_dir / "arXiv-Netscience_weighted.svg"
    draw_weighted_multiplex("arXiv-Netscience", output_path, connect_adjacent_only=True)
    print("完成 arXiv-Netscience 带权重可视化")
