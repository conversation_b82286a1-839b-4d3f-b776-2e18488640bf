import pathlib
import matplotlib
matplotlib.use("Agg")
import matplotlib.pyplot as plt
import networkx as nx
import numpy as np

ROOT_DIR = pathlib.Path(__file__).parent.parent
MLN_DIR = ROOT_DIR / "MLNDatasets"


def load_layer_from_txt(layer_file_path: pathlib.Path, directed: bool = False) -> nx.Graph:
    """从txt文件加载单层网络"""
    graph = nx.DiGraph() if directed else nx.Graph()
    
    if not layer_file_path.exists():
        return graph
    
    with layer_file_path.open("r", encoding="utf-8-sig", errors="replace") as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            parts = line.split()
            if len(parts) >= 2:
                try:
                    u = int(parts[0])
                    v = int(parts[1])
                    graph.add_edge(u, v)
                except ValueError:
                    # 处理非数字节点ID的情况
                    u = parts[0]
                    v = parts[1]
                    graph.add_edge(u, v)
    
    return graph


def load_multi_layer_graphs_from_files(dataset_dir: pathlib.Path, directed: bool = False) -> dict:
    """从分层文件加载多层网络图"""
    graphs = {}
    
    # 查找所有layer文件
    layer_files = list(dataset_dir.glob("layer*.txt"))
    layer_files.sort()  # 确保按顺序处理
    
    for layer_file in layer_files:
        # 从文件名提取层ID
        layer_name = layer_file.stem  # 例如 "layer0", "layer1"
        if layer_name.startswith("layer"):
            try:
                layer_id = int(layer_name[5:])  # 提取数字部分
                graph = load_layer_from_txt(layer_file, directed)
                if graph.number_of_nodes() > 0:
                    graphs[layer_id] = graph
                    print(f"加载层 {layer_id}: {graph.number_of_nodes()} 节点, {graph.number_of_edges()} 边")
            except ValueError:
                print(f"无法解析层文件名: {layer_file}")
    
    return graphs


def compute_layered_positions(graphs: dict[int, nx.Graph], all_nodes: set, 
                            y_gap: float = 1.5, seed: int = 42, iterations: int = 50) -> dict:
    """计算多层网络的节点位置（标准风格）"""
    np.random.seed(seed)
    
    # 显示所有节点以完整展示拓扑结构
    print(f"显示所有节点: {len(all_nodes)}个")
    
    # 自适应选择布局策略
    n = len(all_nodes)
    if n > 2000:
        iterations = 20
    elif n > 1000:
        iterations = 30
    else:
        iterations = 50
    
    # 创建包含所有节点的临时图
    temp_graph = nx.Graph()
    temp_graph.add_nodes_from(all_nodes)
    
    # 添加所有层的边来改善布局
    for layer_graph in graphs.values():
        for u, v in layer_graph.edges():
            if u in all_nodes and v in all_nodes:
                temp_graph.add_edge(u, v)
    
    # 生成基础布局
    if temp_graph.number_of_edges() > 0:
        base_pos = nx.spring_layout(temp_graph, k=1, iterations=iterations, seed=seed)
    else:
        # 如果没有边，使用圆形布局
        base_pos = nx.circular_layout(temp_graph)
    
    # 为每层分配位置
    positions = {}
    layer_ids = sorted(graphs.keys())
    
    for i, layer_id in enumerate(layer_ids):
        offset_y = i * y_gap
        for v in all_nodes:
            x, y = base_pos.get(v, (0.0, 0.0))
            positions[(layer_id, v)] = (x, y + offset_y)
    
    return positions


def draw_multi_layer_network(dataset_name: str, output_path: pathlib.Path, 
                           connect_adjacent_only: bool = True):
    """绘制多层网络（标准风格）"""
    
    # 加载数据
    dataset_dir = MLN_DIR / dataset_name
    
    if not dataset_dir.exists():
        raise FileNotFoundError(f"数据集目录不存在: {dataset_dir}")
    
    # 加载多层图数据
    graphs = load_multi_layer_graphs_from_files(dataset_dir, directed=False)
    
    if not graphs:
        raise ValueError("未找到有效的层数据")
    
    # 获取所有节点
    all_nodes = set()
    for graph in graphs.values():
        all_nodes.update(graph.nodes())
    
    # 显示所有节点以完整展示拓扑结构
    print(f"显示所有节点: {len(all_nodes)}个")
    
    # 过滤图，只保留选定的节点
    filtered_graphs = {}
    for layer_id, graph in graphs.items():
        filtered_graph = graph.subgraph(all_nodes).copy()
        if filtered_graph.number_of_nodes() > 0:
            filtered_graphs[layer_id] = filtered_graph
    
    print(f"过滤后的层数: {len(filtered_graphs)}")
    print(f"过滤后的节点数: {len(all_nodes)}")
    
    # 计算位置（使用标准风格）
    positions = compute_layered_positions(filtered_graphs, all_nodes)
    
    # 创建图形（使用标准风格）
    layer_ids = sorted(filtered_graphs.keys())
    colors = [
        "tab:blue", "tab:orange", "tab:green", "tab:red", "tab:purple",
        "tab:brown", "tab:pink", "tab:gray", "tab:olive", "tab:cyan"
    ]
    
    plt.figure(figsize=(10, 2 + 2 * len(layer_ids)))
    ax = plt.gca()
    
    # 绘制层内连接（实线，不同颜色）
    for idx, layer_id in enumerate(layer_ids):
        graph = filtered_graphs[layer_id]
        layer_color = colors[idx % len(colors)]
        
        # 为当前层的图添加所有节点（保持布局一致性）
        graph_with_all_nodes = graph.copy()
        graph_with_all_nodes.add_nodes_from(all_nodes)
        
        # 绘制节点
        nx.draw_networkx_nodes(
            graph_with_all_nodes,
            {v: positions[(layer_id, v)] for v in all_nodes},
            node_size=35,
            node_color="#f0f0f0",
            edgecolors="#333333", 
            linewidths=0.4,
            ax=ax,
        )
        
        # 绘制边（实线）
        for u, v in graph.edges():
            if u in all_nodes and v in all_nodes:
                pos_u = positions[(layer_id, u)]
                pos_v = positions[(layer_id, v)]
                
                ax.plot([pos_u[0], pos_v[0]], [pos_u[1], pos_v[1]], 
                       color=layer_color, linewidth=0.8, alpha=0.7)
        
        # 添加层标签
        ys = [positions[(layer_id, v)][1] for v in all_nodes]
        y_level = sum(ys) / len(ys) if ys else idx * 1.5
        ax.text(1.02, y_level, f"Layer {layer_id}", 
               transform=ax.transData, fontsize=10, va='center')
    
    # 绘制层间连接（虚线，标准风格）
    if connect_adjacent_only and len(layer_ids) > 1:
        for i in range(len(layer_ids) - 1):
            layer1_id = layer_ids[i]
            layer2_id = layer_ids[i + 1]
            
            # 连接相邻层的相同节点
            for v in all_nodes:
                pos1 = positions[(layer1_id, v)]
                pos2 = positions[(layer2_id, v)]
                ax.plot([pos1[0], pos2[0]], [pos1[1], pos2[1]], 
                       linestyle=(0, (4, 4)), color="#777777", linewidth=0.6, alpha=0.7)
    
    # 设置图形属性（标准风格）
    ax.axis("off")
    plt.tight_layout()
    
    # 确保输出目录存在
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 保存图形（保存SVG和PNG两种格式）
    stem = output_path.with_suffix("")
    svg_path = stem.with_suffix(".svg")
    png_path = stem.with_suffix(".png")
    
    plt.savefig(svg_path, dpi=300, bbox_inches="tight")
    plt.savefig(png_path, dpi=300, bbox_inches="tight")
    plt.close()
    
    print(f"Saved figures to: {svg_path} and {png_path}")
    print(f"图形已保存到: {svg_path}")
    print(f"数据集: {dataset_name}")
    print(f"原始层数: {len(graphs)}, 过滤后层数: {len(filtered_graphs)}")
    print(f"显示节点数: {len(all_nodes)}")
    print(f"显示边总数: {sum(g.number_of_edges() for g in filtered_graphs.values())}")


if __name__ == "__main__":
    output_dir = pathlib.Path("MLN_SVGs")
    output_dir.mkdir(exist_ok=True)
    
    output_path = output_dir / "Multi-Soc-wiki-Vote.svg"
    draw_multi_layer_network("Multi-Soc-wiki-Vote", output_path, connect_adjacent_only=True)
    print("完成 Multi-Soc-wiki-Vote 多层网络可视化")
