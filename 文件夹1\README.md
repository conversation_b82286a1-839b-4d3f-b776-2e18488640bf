# arXiv-Netscience 带权重多层网络可视化

## 概述
这个程序专门用于可视化 arXiv-Netscience 数据集，该数据集的特点是边权重不相等，需要特殊处理。

## 数据集特点
- **数据集名称**: arXiv-Netscience
- **权重范围**: 0.026316 - 17.638889
- **层数**: 13层
- **节点数**: 大量节点（程序限制显示前500个）
- **边数**: 大量边（过滤后约2133条边）

## 程序特性
1. **标准可视化风格**:
   - **层内连接**: 实线，每层使用不同颜色（蓝、橙、绿、红等）
   - **层间连接**: 虚线，灰色，连接相邻层的相同节点
   - **节点样式**: 统一的浅灰色节点，黑色边框

2. **权重可视化**: 根据边权重调整线宽和透明度
   - 权重越大，线条越粗，透明度越高
   - 权重范围: 0.026316 - 17.638889

3. **性能优化**:
   - 限制显示节点数量（最多500个）
   - 使用Spring布局算法，自适应迭代次数
   - 过滤无效连接

4. **布局特点**:
   - 垂直分层，每层有固定的Y偏移
   - 保持节点在不同层的相对位置一致
   - 层标签显示在右侧

## 输出文件
- **SVG文件**: `MLN_SVGs/arXiv-Netscience_weighted.svg` - 矢量图，可无限缩放
- **PNG文件**: `MLN_SVGs/arXiv-Netscience_weighted.png` - 位图，300 DPI
- **图形尺寸**: 自适应，基于层数调整高度

## 使用方法
```bash
cd 文件夹1
python visualize_arXiv_Netscience_weighted.py
```

## 注意事项
- 程序会自动限制节点数量以提高性能
- 中文字体可能显示警告，但不影响图形生成
- 生成的图形包含权重信息的颜色编码

## 技术细节
- **布局算法**: Spring布局 + 层次偏移
- **线宽范围**: 0.5 - 3.0 像素（基于权重）
- **透明度范围**: 0.6 - 0.9（基于权重）
- **层间距**: 1.5 单位
- **节点大小**: 35 像素
- **虚线样式**: (0, (4, 4)) - 4像素线段，4像素间隔
