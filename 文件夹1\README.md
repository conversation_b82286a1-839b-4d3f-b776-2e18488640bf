# arXiv-Netscience 带权重多层网络可视化

## 概述
这个程序专门用于可视化 arXiv-Netscience 数据集，该数据集的特点是边权重不相等，需要特殊处理。

## 数据集特点
- **数据集名称**: arXiv-Netscience
- **权重范围**: 0.026316 - 17.638889
- **层数**: 13层
- **节点数**: 大量节点（程序限制显示前500个）
- **边数**: 大量边（过滤后约2133条边）

## 程序特性
1. **权重可视化**: 根据边权重使用不同颜色和线宽
   - 低权重：浅蓝色，细线
   - 高权重：深红色，粗线

2. **性能优化**: 
   - 限制显示节点数量（最多500个）
   - 使用圆形布局提高计算效率
   - 过滤无效连接

3. **布局特点**:
   - 每层使用不同颜色背景
   - 层间连接用虚线表示
   - 节点按圆形分布

## 输出文件
- **文件位置**: `MLN_SVGs/arXiv-Netscience_weighted.svg`
- **格式**: SVG矢量图
- **分辨率**: 300 DPI

## 使用方法
```bash
cd 文件夹1
python visualize_arXiv_Netscience_weighted.py
```

## 注意事项
- 程序会自动限制节点数量以提高性能
- 中文字体可能显示警告，但不影响图形生成
- 生成的图形包含权重信息的颜色编码

## 技术细节
- **权重颜色映射**: 从浅蓝色到深红色的渐变
- **线宽范围**: 0.5 - 2.5 像素
- **布局算法**: 圆形布局 + 层次偏移
- **图形尺寸**: 20x15 英寸
