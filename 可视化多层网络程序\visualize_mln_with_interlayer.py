import argparse
import pathlib
import matplotlib
matplotlib.use("Agg")
import matplotlib.pyplot as plt
import networkx as nx

ROOT_DIR = pathlib.Path(__file__).parent
MLN_DIR = ROOT_DIR / "MLNDatasets"


def auto_pick_files(dataset_dir: pathlib.Path):
	data_dir = dataset_dir / "Dataset"
	if not data_dir.exists():
		raise FileNotFoundError(f"Dataset folder not found: {data_dir}")
	edges = None
	layers = None
	nodes = None
	for p in sorted(data_dir.iterdir()):
		if p.suffix.lower() == ".edges" and edges is None:
			edges = p
		if p.suffix.lower() == ".txt":
			name_lower = p.name.lower()
			if "layer" in name_lower and layers is None:
				layers = p
			if "node" in name_lower and nodes is None:
				nodes = p
	if edges is None:
		raise FileNotFoundError(f"No .edges file found in {data_dir}")
	return layers, nodes, edges


def load_layers(layer_file_path: pathlib.Path | None) -> dict:
	mapping = {}
	if not layer_file_path or not layer_file_path.exists():
		return mapping
	with layer_file_path.open("r", encoding="utf-8-sig", errors="replace") as f:
		for idx, line in enumerate(f):
			line = line.strip()
			if not line:
				continue
			if idx == 0 and ("layerid" in line.lower() or "layer" in line.lower()):
				# 跳过表头
				continue
			parts = line.split()
			if len(parts) >= 2 and parts[0].isdigit():
				mapping[int(parts[0])] = parts[1]
	return mapping


def load_nodes(nodes_file_path: pathlib.Path | None) -> set:
	node_ids = set()
	if not nodes_file_path or not nodes_file_path.exists():
		return node_ids
	with nodes_file_path.open("r", encoding="utf-8-sig", errors="replace") as f:
		for idx, line in enumerate(f):
			line = line.strip()
			if not line:
				continue
			if idx == 0 and ("nodeid" in line.lower() or "node" in line.lower()):
				continue
			parts = line.split()
			try:
				node_id = int(parts[0])
				node_ids.add(node_id)
			except Exception:
				continue
	return node_ids


def load_layer_graphs(edges_file_path: pathlib.Path, directed: bool = True) -> dict:
	graphs: dict[int, nx.Graph] = {}
	with edges_file_path.open("r", encoding="utf-8-sig", errors="replace") as f:
		for line in f:
			line = line.strip()
			if not line:
				continue
			parts = line.split()
			# 格式假定：layer u v [w]
			if len(parts) < 3:
				continue
			try:
				layer_id = int(parts[0])
				u = int(parts[1])
				v = int(parts[2])
				w = float(parts[3]) if len(parts) > 3 else 1.0
			except Exception:
				continue
			if layer_id not in graphs:
				graphs[layer_id] = nx.DiGraph() if directed else nx.Graph()
			graphs[layer_id].add_edge(u, v, weight=w)
	return graphs


def compute_layered_positions(
	graphs: dict[int, nx.Graph],
	all_nodes: set,
	y_gap: float = 1.5,
	seed: int = 42,
	algorithm: str = "spring",
	iterations: int = 50,
) -> dict[tuple[int, int], tuple[float, float]]:
	# 先在联合图上计算基础布局
	union = nx.DiGraph()
	union.add_nodes_from(all_nodes)
	for g in graphs.values():
		union.add_nodes_from(g.nodes)
		union.add_edges_from(g.edges)
	if algorithm == "spring":
		base_pos = nx.spring_layout(union, seed=seed, iterations=max(1, int(iterations)))
	elif algorithm == "kamada":
		base_pos = nx.kamada_kawai_layout(union)
	else:
		base_pos = nx.spring_layout(union, seed=seed, iterations=max(1, int(iterations)))
	# 将每层沿 y 方向平移
	positions: dict[tuple[int, int], tuple[float, float]] = {}
	for idx, layer_id in enumerate(sorted(graphs.keys())):
		offset_y = idx * y_gap
		for v in all_nodes:
			x, y = base_pos.get(v, (0.0, 0.0))
			positions[(layer_id, v)] = (x, y + offset_y)
	return positions


def draw_multiplex_with_interlayer(dataset_name: str, outfile: pathlib.Path, connect_adjacent_only: bool = True):
	ds_dir = MLN_DIR / dataset_name
	layers_file, nodes_file, edges_file = auto_pick_files(ds_dir)
	layer_labels = load_layers(layers_file)
	node_ids = load_nodes(nodes_file)
	graphs = load_layer_graphs(edges_file, directed=True)
	if not graphs:
		raise ValueError("No graphs parsed from edges file")
	# 汇总所有节点
	all_nodes = set(node_ids)
	for g in graphs.values():
		all_nodes.update(g.nodes)

	# 自适应选择布局策略与迭代次数（避免大图长时间停滞）
	n = len(all_nodes)
	if n > 5000:
		algorithm = "kamada"
		iterations = 1
	elif n > 2000:
		algorithm = "spring"
		iterations = 20
	else:
		algorithm = "spring"
		iterations = 50

	positions = compute_layered_positions(graphs, all_nodes, algorithm=algorithm, iterations=iterations)

	layer_ids = sorted(graphs.keys())
	colors = [
		"tab:blue", "tab:orange", "tab:green", "tab:red", "tab:purple",
		"tab:brown", "tab:pink", "tab:gray", "tab:olive", "tab:cyan",
	]

	plt.figure(figsize=(10, 2 + 2 * len(layer_ids)))
	ax = plt.gca()

	# 画层内（实线）
	for idx, layer_id in enumerate(layer_ids):
		g = graphs[layer_id]
		g.add_nodes_from(all_nodes)
		layer_color = colors[idx % len(colors)]
		# 节点
		nx.draw_networkx_nodes(
			g,
			{v: positions[(layer_id, v)] for v in all_nodes},
			node_size=35,
			node_color="#f0f0f0",
			edgecolors="#333333",
			linewidths=0.4,
			ax=ax,
		)
		# 边（实线）
		nx.draw_networkx_edges(
			g,
			{v: positions[(layer_id, v)] for v in all_nodes},
			arrows=True,
			arrowstyle="-|>",
			arrowsize=8,
			edge_color=layer_color,
			width=1.0,
			alpha=0.8,
			ax=ax,
		)
		# 层标题
		ys = [positions[(layer_id, v)][1] for v in all_nodes]
		y_level = sum(ys) / len(ys) if ys else idx
		ax.text(1.02, y_level, f"Layer {layer_id}: {layer_labels.get(layer_id, '')}", transform=ax.transData)

	# 画层间（虚线）——连接相邻层的同一节点副本，减少杂乱
	for i in range(len(layer_ids) - 1):
		la = layer_ids[i]
		lb = layer_ids[i + 1] if connect_adjacent_only else None
		for v in all_nodes:
			pa = positions[(la, v)]
			if connect_adjacent_only:
				pb = positions[(lb, v)]
				ax.plot([pa[0], pb[0]], [pa[1], pb[1]], linestyle=(0, (4, 4)), color="#777777", linewidth=0.6, alpha=0.7)
			else:
				# 连接到所有更高层（可选）
				for j in range(i + 1, len(layer_ids)):
					pb = positions[(layer_ids[j], v)]
					ax.plot([pa[0], pb[0]], [pa[1], pb[1]], linestyle=(0, (4, 4)), color="#777777", linewidth=0.4, alpha=0.5)

	ax.axis("off")
	plt.tight_layout()
	outfile.parent.mkdir(parents=True, exist_ok=True)
	# 保存 svg 与 png 两份
	suffix = outfile.suffix.lower()
	stem = outfile.with_suffix("")
	svg_path = stem.with_suffix(".svg")
	png_path = stem.with_suffix(".png")
	plt.savefig(svg_path, dpi=300, bbox_inches="tight")
	plt.savefig(png_path, dpi=300, bbox_inches="tight")
	print(f"Saved figures to: {svg_path} and {png_path}")


def main():
	parser = argparse.ArgumentParser(description="Visualize multilayer network with inter-layer dashed connections")
	parser.add_argument("--dataset", type=str, default="CKM", help="Name of dataset folder under MLNDatasets (e.g., CKM, EUAir, CElegans)")
	parser.add_argument("--output", type=str, default="multilayer_interlayer.png", help="Output image path")
	parser.add_argument("--all-pairs", action="store_true", help="Connect all layer pairs for each node (default: only adjacent layers)")
	args = parser.parse_args()

	outfile = pathlib.Path(args.output)
	draw_multiplex_with_interlayer(args.dataset, outfile, connect_adjacent_only=not args.all_pairs)


if __name__ == "__main__":
	main() 