# Multi-Soc-wiki-Vote 分层文件多层网络可视化

## 概述
这个程序专门用于可视化 Multi-Soc-wiki-Vote 数据集，该数据集的特点是数据分布在不同的层文件中，而不是统一的Dataset文件夹。

## 数据集特点
- **数据集名称**: Multi-Soc-wiki-Vote
- **数据结构**: 分层文件格式
  - `layer0.txt`: 第0层网络数据
  - `layer1.txt`: 第1层网络数据  
  - `layer2.txt`: 第2层网络数据
  - `layer3.txt`: 第3层网络数据
- **层数**: 4层
- **节点数**: 889个节点（显示前800个）
- **边数**: 总计8892条边（过滤后7919条）

## 数据格式
每个层文件的格式为：
```
节点1 节点2
节点1 节点3
...
```
例如：
```
8 1
8 2
8 3
```

## 程序特性
1. **标准可视化风格**: 
   - **层内连接**: 实线，每层使用不同颜色（蓝、橙、绿、红）
   - **层间连接**: 虚线，灰色，连接相邻层的相同节点
   - **节点样式**: 统一的浅灰色节点，黑色边框

2. **分层文件处理**: 
   - 自动识别和加载所有layer*.txt文件
   - 按层ID排序处理
   - 支持数字节点ID

3. **性能优化**: 
   - 限制显示节点数量（最多800个）
   - 使用Spring布局算法，自适应迭代次数
   - 过滤无效连接

4. **布局特点**:
   - 垂直分层，每层有固定的Y偏移
   - 保持节点在不同层的相对位置一致
   - 层标签显示在右侧

## 输出文件
- **SVG文件**: `MLN_SVGs/Multi-Soc-wiki-Vote.svg` - 矢量图，可无限缩放
- **PNG文件**: `MLN_SVGs/Multi-Soc-wiki-Vote.png` - 位图，300 DPI
- **图形尺寸**: 自适应，基于层数调整高度

## 使用方法
```bash
cd 文件夹3
python visualize_Multi_Soc_wiki_Vote.py
```

## 层信息说明
- **Layer 0**: 889个节点，2914条边
- **Layer 1**: 889个节点，1641条边
- **Layer 2**: 889个节点，2418条边
- **Layer 3**: 889个节点，1919条边

## 注意事项
- 程序会自动限制节点数量以提高性能
- 中文字体可能显示警告，但不影响图形生成
- 所有层共享相同的节点集合

## 技术细节
- **布局算法**: Spring布局 + 层次偏移
- **层间距**: 1.5 单位
- **节点大小**: 35 像素
- **虚线样式**: (0, (4, 4)) - 4像素线段，4像素间隔
- **文件编码**: UTF-8-sig，支持BOM
- **错误处理**: 自动跳过无效行和空行

## 数据来源
Multi-Soc-wiki-Vote 数据集来源于社交网络投票数据，包含多个层次的用户交互关系。
