# 分层文件数据集处理说明

## 问题背景
在多层网络可视化过程中，发现 Multi-Soc-wiki-Vote 和 Multi_lastfm_asia 这两个数据集存在特殊情况：
- **无Dataset文件夹**: 数据集没有统一的Dataset文件夹结构
- **分层文件格式**: 每层数据分别存储在独立的layer*.txt文件中
- **混合节点类型**: 部分数据集包含字符串和数字混合的节点ID

## 解决方案
为这两个特殊数据集分别创建了专门的处理程序：

### 文件夹3 - Multi-Soc-wiki-Vote 处理
- **程序文件**: `visualize_Multi_Soc_wiki_Vote.py`
- **输出文件**: `MLN_SVGs/Multi-Soc-wiki-Vote.svg` 和 `.png`
- **特点**: 
  - 4层网络结构（layer0-layer3）
  - 889个节点，显示前800个
  - 纯数字节点ID

### 文件夹4 - Multi_lastfm_asia 处理
- **程序文件**: `visualize_Multi_lastfm_asia.py`
- **输出文件**: `MLN_SVGs/Multi_lastfm_asia.svg` 和 `.png`
- **特点**:
  - 2层网络结构（layer0-layer1）
  - 7624个节点，显示前600个
  - 混合节点ID（数字+字符串）

## 技术改进

### 1. 分层文件加载
- **自动识别**: 扫描目录中的所有layer*.txt文件
- **按序处理**: 根据层ID排序加载
- **格式解析**: 支持"节点1 节点2"的边列表格式
- **错误处理**: 自动跳过无效行和空行

### 2. 混合节点ID处理
- **类型检测**: 自动识别数字和字符串节点
- **ID标准化**: 将字符串节点映射为连续整数
- **关系保持**: 确保边连接关系的完整性
- **映射记录**: 维护原始ID到标准ID的映射

### 3. 标准可视化风格
- **层内连接**: 实线，每层使用不同颜色
- **层间连接**: 虚线，灰色，连接相邻层相同节点
- **节点样式**: 统一的浅灰色节点，黑色边框
- **布局算法**: Spring布局 + 层次偏移

### 4. 性能优化
- **节点限制**: 
  - Multi-Soc-wiki-Vote: 最多800个节点
  - Multi_lastfm_asia: 最多600个节点
- **自适应算法**: 根据数据规模调整迭代次数
- **内存优化**: 及时释放临时数据结构

## 数据集对比

| 数据集 | 层数 | 原始节点数 | 显示节点数 | 原始边数 | 显示边数 | 节点类型 |
|--------|------|------------|------------|----------|----------|----------|
| Multi-Soc-wiki-Vote | 4 | 889 | 800 | 8892 | 7919 | 数字 |
| Multi_lastfm_asia | 2 | 7624 | 600 | 42890 | 256 | 混合 |

## 文件结构对比

### Multi-Soc-wiki-Vote
```
MLNDatasets/Multi-Soc-wiki-Vote/
├── layer0.txt  (2914 边)
├── layer1.txt  (1641 边)
├── layer2.txt  (2418 边)
├── layer3.txt  (1919 边)
└── edge.txt    (其他文件)
```

### Multi_lastfm_asia
```
MLNDatasets/Multi_lastfm_asia/
├── layer0.txt  (27806 边)
├── layer1.txt  (15084 边)
└── edge.txt    (其他文件)
```

## 使用说明

### 运行 Multi-Soc-wiki-Vote 可视化
```bash
cd 文件夹3
python visualize_Multi_Soc_wiki_Vote.py
```

### 运行 Multi_lastfm_asia 可视化
```bash
cd 文件夹4
python visualize_Multi_lastfm_asia.py
```

## 输出结果
两个程序都会生成高质量的图形文件，包含：
- **SVG矢量图**: 可无限缩放，适合学术发表
- **PNG位图**: 300 DPI，适合文档嵌入
- **标准化风格**: 与现有可视化程序保持一致
- **层次结构**: 清晰的多层网络展示
- **层标签**: 明确的层次标识

## 技术特点
- **灵活的文件格式支持**: 适应不同的数据组织方式
- **混合数据类型处理**: 统一处理数字和字符串节点
- **标准化可视化风格**: 保持与现有程序的一致性
- **性能优化策略**: 处理大规模网络数据
- **双格式输出**: SVG矢量图 + PNG位图
- **自适应布局**: 根据数据特点调整算法参数

## 注意事项
1. **数据规模**: 大规模数据集会自动限制显示节点数量
2. **字体警告**: 可能出现中文字体缺失警告，但不影响图形生成
3. **处理时间**: 根据数据规模，处理时间可能从几秒到几分钟不等
4. **内存使用**: 建议在内存充足的环境下运行
5. **节点映射**: Multi_lastfm_asia的字符串节点会被自动映射为整数

## 扩展性
这两个程序的设计具有良好的扩展性：
- 可以轻松适配其他分层文件格式的数据集
- 支持添加更多的节点ID类型处理
- 可以调整性能参数以适应不同规模的数据
- 布局算法可以根据需要进行替换或优化
